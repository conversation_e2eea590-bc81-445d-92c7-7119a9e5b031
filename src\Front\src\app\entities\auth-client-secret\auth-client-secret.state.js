(function () {
    'use strict';

    angular.module('bbcWeb.auth-client-secret.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('auth-client-secret', {
            abstract: true,
            url: "/auth-client-secret",
            templateUrl: "app/layout/content.html"
        }).state('auth-client-secret.index', {
            url: '/index',
            templateUrl: 'app/entities/auth-client-secret/auth-client-secret.html'
        }).state('auth-client-secret.auth-client-secret-crud', {
            url: '/:link',
            templateUrl: 'app/entities/auth-client-secret/auth-client-secret-crud.html'
        });
    }
})();
