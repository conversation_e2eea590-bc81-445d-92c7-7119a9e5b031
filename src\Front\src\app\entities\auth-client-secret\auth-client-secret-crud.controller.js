(function () {
    'use strict';

    angular.module('bbcWeb').controller('AuthClientSecretCrudController', AuthClientSecretCrudController);

    AuthClientSecretCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function AuthClientSecretCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isSaving = false;

        vm.voltar = function () {
            $state.go('auth-client-secret.index');
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving) return;

            vm.isSaving = true;

            var saveAuthClientSecret = {
                login: vm.authClientSecret.login,
                senha: vm.authClientSecret.senha,
                clientSecret: vm.authClientSecret.clientSecret,
                descricao: vm.authClientSecret.descricao,
                id: vm.authClientSecret.id === "Auto" ? "0" : vm.authClientSecret.id
            }

            if (!vm.isNew() && !vm.authClientSecret.senha) {
                delete saveAuthClientSecret.senha; // Não alterar senha se não informada
            }

            abrirModal(saveAuthClientSecret);
        };

        function abrirModal(saveAuthClientSecret) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/auth-client-secret/modal/auth-client-secret-modal.html',
                controller: function ($uibModalInstance, toastr, BaseService, saveAuthClientSecret, $rootScope) {
                    var vm = this;
                    vm.carregandoEdit = true;

                    BaseService.post('AuthClientSecret', 'SaveAuthClientSecret', saveAuthClientSecret).then(function (response) {
                        vm.carregandoEdit = false;
                        if (response.success) {
                            toastr.success(response.message);
                            vm.authClientSecret = response.data
                        } else {
                            $uibModalInstance.close();
                            toastr.error(response.message);
                        }

                    });

                    vm.closeModal = function () {
                        $uibModalInstance.close();
                        $state.go('auth-client-secret.index');
                    };

                    vm.copiar = function () {
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(vm.authClientSecret).then(function () {
                                toastr.success('Client Secret copiado para a área de transferência!');
                            });
                        } else {
                            // Fallback para navegadores mais antigos
                            var textArea = document.createElement("textarea");
                            textArea.value = vm.authClientSecret;
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            try {
                                document.execCommand('copy');
                                toastr.success('Client Secret copiado para a área de transferência!');
                            } catch (err) {
                                toastr.error('Erro ao copiar Client Secret');
                            }
                            document.body.removeChild(textArea);
                        }
                    };
                },
                controllerAs: 'vm',
                resolve: {
                    saveAuthClientSecret: function () {
                        return saveAuthClientSecret;
                    }
                }
            }).result.then(function () {
                vm.isSaving = false;
            }, function () {
                vm.isSaving = false;
            });
        }

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Auth Client Secret',
            link: 'auth-client-secret.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.authClientSecret = {
            ativo: 1
        };

        // Carregar dados se for edição
        if (!vm.isNew()) {
            BaseService.get('AuthClientSecret', 'ConsultarPorId', { id: $stateParams.link }).then(function (response) {
                if (response.success) {
                    vm.authClientSecret = response.data;
                    vm.authClientSecret.senha = ''; // Não carregar senha por segurança
                } else {
                    toastr.error(response.message);
                    vm.voltar();
                }
            });
        }
    }
})();
