(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('AuthClientSecretController', AuthClientSecretController);

    AuthClientSecretController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function AuthClientSecretController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Auth Client Secret'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "AuthClientSecret/ConsultarGridAuthClientSecret"
            },
            columnDefs: [
                {
                    name: '<PERSON><PERSON><PERSON><PERSON>',
                    field: 'acoes',
                    width: 120,
                    enableSorting: false,
                    enableFiltering: false,
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<button type="button" class="btn btn-xs btn-primary" ng-click="grid.appScope.vm.editar(row.entity.id)" uib-tooltip="Editar">' +
                        '<i class="fa fa-edit"></i>' +
                        '</button> ' +
                        '<button type="button" class="btn btn-xs" ng-class="row.entity.ativo == 1 ? \'btn-danger\' : \'btn-success\'" ng-click="grid.appScope.vm.alterarStatus(row.entity)" uib-tooltip="{{row.entity.ativo == 1 ? \'Desativar\' : \'Ativar\'}}">' +
                        '<i class="fa" ng-class="row.entity.ativo == 1 ? \'fa-times\' : \'fa-check\'"></i>' +
                        '</button>' +
                        '</div>'
                },
                {
                    name: 'Login',
                    field: 'login',
                    width: 150,
                    serverField: 'login'
                },
                {
                    name: 'Descrição',
                    field: 'descricao',
                    width: 200,
                    serverField: 'descricao'
                },
                {
                    name: 'Client Secret',
                    field: 'clientSecret',
                    width: 250,
                    enableFiltering: false,
                    cellTemplate: '<div class="ui-grid-cell-contents">' +
                        '<span>{{row.entity.clientSecret.substring(0, 20)}}...</span> ' +
                        '<button type="button" class="btn btn-xs btn-info" ng-click="grid.appScope.vm.visualizarClientSecret(row.entity.clientSecret)" uib-tooltip="Visualizar">' +
                        '<i class="fa fa-eye"></i>' +
                        '</button>' +
                        '</div>'
                },
                {
                    name: 'Status',
                    field: 'ativo',
                    width: 80,
                    serverField: 'ativo',
                    cellTemplate: '<div class="ui-grid-cell-contents text-center">' +
                        '<span class="label" ng-class="row.entity.ativo == 1 ? \'label-success\' : \'label-danger\'">' +
                        '{{row.entity.ativo == 1 ? "Ativo" : "Inativo"}}' +
                        '</span>' +
                        '</div>'
                },
                {
                    name: 'Data Cadastro',
                    field: 'dataCadastro',
                    width: 130,
                    enableFiltering: false
                },
                {
                    name: 'Data Alteração',
                    field: 'dataAlteracao',
                    width: 130,
                    enableFiltering: false
                },
                {
                    name: 'Usuário Cadastro',
                    field: 'usuarioCadastro',
                    width: 150,
                    enableFiltering: false
                },
                {
                    name: 'Usuário Alteração',
                    field: 'usuarioAlteracao',
                    width: 150,
                    enableFiltering: false
                }
            ]
        };

        vm.editar = function (id) {
            $state.go('auth-client-secret.auth-client-secret-crud', { link: id });
        };

        vm.alterarStatus = function (entity) {
            var novoStatus = entity.ativo == 1 ? 0 : 1;
            var mensagem = novoStatus == 1 ? 'ativar' : 'desativar';
            
            if (confirm('Deseja realmente ' + mensagem + ' este auth client secret?')) {
                BaseService.post('AuthClientSecret', 'AlterarStatus', {
                    id: entity.id,
                    ativo: novoStatus
                }).then(function (response) {
                    if (response.success) {
                        toastr.success(response.message);
                        vm.gridOptions.dataSource.refresh();
                    } else {
                        toastr.error(response.message);
                    }
                });
            }
        };

        vm.visualizarClientSecret = function (clientSecret) {
            // Implementar modal para visualizar client secret
            alert('Client Secret: ' + clientSecret);
        };
    }
})();
